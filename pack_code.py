#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码打包工具
创建一个优化的代码压缩包，目标大小约50MB
"""

import os
import zipfile
import json
import shutil
import datetime
from pathlib import Path

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    if os.path.exists(file_path):
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    return 0

def should_exclude_file(file_path, exclude_patterns):
    """检查文件是否应该被排除"""
    file_name = os.path.basename(file_path)
    file_ext = os.path.splitext(file_name)[1].lower()
    
    # 排除的文件扩展名
    excluded_extensions = {
        '.pyc', '.pyo', '.pyd', '__pycache__',
        '.git', '.gitignore', '.DS_Store',
        '.tmp', '.temp', '.log',
        '.exe', '.dll', '.so',
        '.zip', '.rar', '.7z', '.tar', '.gz'
    }
    
    if file_ext in excluded_extensions:
        return True
    
    # 排除的文件名模式
    for pattern in exclude_patterns:
        if pattern in file_name.lower():
            return True
    
    return False

def create_optimized_package():
    """创建优化的代码包"""
    
    # 当前目录
    current_dir = os.getcwd()
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 输出文件名
    output_filename = f"code_package_{timestamp}.zip"
    
    # 排除模式
    exclude_patterns = [
        'test', 'tests', 'debug', 'temp', 'tmp',
        'cache', 'backup', 'old', '备份',
        '__pycache__', '.git', 'node_modules'
    ]
    
    print(f"🚀 开始创建代码包: {output_filename}")
    print(f"📁 源目录: {current_dir}")
    
    # 统计信息
    total_files = 0
    included_files = 0
    total_size = 0
    
    try:
        with zipfile.ZipFile(output_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            
            # 遍历当前目录
            for root, dirs, files in os.walk(current_dir):
                
                # 排除某些目录
                dirs[:] = [d for d in dirs if not should_exclude_file(os.path.join(root, d), exclude_patterns)]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    total_files += 1
                    
                    # 跳过输出文件本身
                    if file == output_filename:
                        continue
                    
                    # 检查是否应该排除
                    if should_exclude_file(file_path, exclude_patterns):
                        print(f"⏭️  跳过: {file}")
                        continue
                    
                    # 计算相对路径
                    rel_path = os.path.relpath(file_path, current_dir)
                    
                    # 添加到压缩包
                    try:
                        zipf.write(file_path, rel_path)
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        included_files += 1
                        
                        # 显示较大的文件
                        if file_size > 1024 * 1024:  # 大于1MB
                            print(f"📄 添加大文件: {rel_path} ({file_size / (1024*1024):.1f}MB)")
                        else:
                            print(f"📄 添加: {rel_path}")
                            
                    except Exception as e:
                        print(f"❌ 添加文件失败 {rel_path}: {e}")
    
    except Exception as e:
        print(f"❌ 创建压缩包失败: {e}")
        return False
    
    # 检查输出文件
    if os.path.exists(output_filename):
        output_size = get_file_size_mb(output_filename)
        compression_ratio = (1 - output_size / (total_size / (1024*1024))) * 100 if total_size > 0 else 0
        
        print(f"\n✅ 打包完成!")
        print(f"📊 统计信息:")
        print(f"   - 总文件数: {total_files}")
        print(f"   - 包含文件数: {included_files}")
        print(f"   - 原始大小: {total_size / (1024*1024):.2f}MB")
        print(f"   - 压缩后大小: {output_size:.2f}MB")
        print(f"   - 压缩率: {compression_ratio:.1f}%")
        print(f"📦 输出文件: {output_filename}")
        
        # 检查是否符合大小要求
        if output_size <= 50:
            print(f"✅ 文件大小符合要求 (≤50MB)")
        else:
            print(f"⚠️  文件大小超出要求 ({output_size:.2f}MB > 50MB)")
            print(f"💡 建议: 可以进一步优化或排除更多文件")
        
        return True
    else:
        print(f"❌ 输出文件不存在")
        return False

def create_minimal_package():
    """创建最小化包（仅核心文件）"""
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"code_minimal_{timestamp}.zip"
    
    # 核心文件列表
    core_files = [
        'my_script.py',
        'app_config.json'
    ]
    
    # 可选文件（如果存在）
    optional_files = [
        'README.md',
        'requirements.txt',
        'setup.py'
    ]
    
    print(f"🚀 创建最小化包: {output_filename}")
    
    try:
        with zipfile.ZipFile(output_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            
            total_size = 0
            file_count = 0
            
            # 添加核心文件
            for file_name in core_files:
                if os.path.exists(file_name):
                    zipf.write(file_name, file_name)
                    file_size = os.path.getsize(file_name)
                    total_size += file_size
                    file_count += 1
                    print(f"📄 添加核心文件: {file_name} ({file_size / 1024:.1f}KB)")
                else:
                    print(f"⚠️  核心文件不存在: {file_name}")
            
            # 添加可选文件
            for file_name in optional_files:
                if os.path.exists(file_name):
                    zipf.write(file_name, file_name)
                    file_size = os.path.getsize(file_name)
                    total_size += file_size
                    file_count += 1
                    print(f"📄 添加可选文件: {file_name} ({file_size / 1024:.1f}KB)")
            
            # 添加8.5文件夹（如果存在）
            if os.path.exists('8.5') and os.path.isdir('8.5'):
                for root, dirs, files in os.walk('8.5'):
                    for file in files:
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path)
                        zipf.write(file_path, rel_path)
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        file_count += 1
                        print(f"📄 添加8.5文件: {rel_path} ({file_size / 1024:.1f}KB)")
    
    except Exception as e:
        print(f"❌ 创建最小化包失败: {e}")
        return False
    
    # 检查结果
    if os.path.exists(output_filename):
        output_size = get_file_size_mb(output_filename)
        print(f"\n✅ 最小化包创建完成!")
        print(f"📊 统计信息:")
        print(f"   - 文件数: {file_count}")
        print(f"   - 原始大小: {total_size / (1024*1024):.2f}MB")
        print(f"   - 压缩后大小: {output_size:.2f}MB")
        print(f"📦 输出文件: {output_filename}")
        return True
    
    return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 代码打包工具")
    print("=" * 60)
    
    print("\n请选择打包方式:")
    print("1. 完整打包 (包含所有相关文件)")
    print("2. 最小化打包 (仅核心文件)")
    print("3. 两种方式都创建")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            create_optimized_package()
        elif choice == "2":
            create_minimal_package()
        elif choice == "3":
            print("\n🔄 创建完整包...")
            create_optimized_package()
            print("\n🔄 创建最小化包...")
            create_minimal_package()
        else:
            print("❌ 无效选择，默认创建最小化包")
            create_minimal_package()
            
    except KeyboardInterrupt:
        print("\n\n❌ 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    print("\n🎉 打包完成!")
